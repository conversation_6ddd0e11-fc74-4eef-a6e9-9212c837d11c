"""
对称加密工具类
使用AES加密算法对敏感数据进行加密和解密
"""

import base64
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import Optional
import os
import logging

logger = logging.getLogger(__name__)


class CryptoUtils:
    """对称加密工具类"""
    
    def __init__(self):
        # 使用固定盐值，确保相同密码生成相同密钥
        self._salt = b'linuxdo_adt_salt_2024_secure_key'
    
    def generate_key_from_password(self, password: str) -> bytes:
        """
        从密码生成加密密钥
        
        Args:
            password: 用户设置的二级密码
            
        Returns:
            bytes: 生成的加密密钥
        """
        if not password:
            raise ValueError("密码不能为空")
        
        try:
            password_bytes = password.encode('utf-8')
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self._salt,
                iterations=100000,  # 使用较高的迭代次数增强安全性
            )
            key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
            return key
        except Exception as e:
            logger.error(f"生成密钥失败: {str(e)}")
            raise ValueError(f"生成密钥失败: {str(e)}")
    
    def encrypt_data(self, data: str, password: str) -> str:
        """
        加密数据
        
        Args:
            data: 要加密的数据
            password: 二级密码
            
        Returns:
            str: 加密后的数据（Base64编码）
        """
        if not data:
            return data
        
        if not password:
            logger.warning("未提供二级密码，数据将不被加密")
            return data
        
        try:
            key = self.generate_key_from_password(password)
            fernet = Fernet(key)
            encrypted_data = fernet.encrypt(data.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            logger.error(f"加密失败: {str(e)}")
            # 加密失败时返回原数据，但记录错误
            return data
    
    def decrypt_data(self, encrypted_data: str, password: str) -> str:
        """
        解密数据
        
        Args:
            encrypted_data: 加密的数据
            password: 二级密码
            
        Returns:
            str: 解密后的数据
        """
        if not encrypted_data:
            return encrypted_data
        
        if not password:
            logger.warning("未提供二级密码，数据将不被解密")
            return encrypted_data
        
        try:
            key = self.generate_key_from_password(password)
            fernet = Fernet(key)
            
            # 解码Base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            
            # 解密
            decrypted_data = fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            # 解密失败时返回原数据，可能是未加密的数据
            return encrypted_data
    
    def is_encrypted(self, data: str) -> bool:
        """
        检查数据是否已加密
        
        Args:
            data: 要检查的数据
            
        Returns:
            bool: 如果数据看起来像是加密的则返回True
        """
        if not data:
            return False
        
        try:
            # 尝试Base64解码，如果成功且长度合理，可能是加密数据
            decoded = base64.urlsafe_b64decode(data.encode('utf-8'))
            # 加密后的数据通常比原数据长，且有一定的最小长度
            return len(decoded) > 32  # Fernet加密后的最小长度
        except Exception:
            return False
    
    def verify_password(self, password: str) -> bool:
        """
        验证密码强度
        
        Args:
            password: 要验证的密码
            
        Returns:
            bool: 密码是否符合要求
        """
        if not password:
            return False
        
        # 基本要求：至少6个字符
        if len(password) < 6:
            return False
        
        return True


# 全局实例
crypto_utils = CryptoUtils()


def encrypt_password(password: str, master_password: Optional[str] = None) -> str:
    """
    加密密码的便捷函数
    
    Args:
        password: 要加密的密码
        master_password: 二级密码，如果为None则不加密
        
    Returns:
        str: 加密后的密码或原密码
    """
    if not master_password:
        return password
    return crypto_utils.encrypt_data(password, master_password)


def decrypt_password(encrypted_password: str, master_password: Optional[str] = None) -> str:
    """
    解密密码的便捷函数
    
    Args:
        encrypted_password: 加密的密码
        master_password: 二级密码，如果为None则不解密
        
    Returns:
        str: 解密后的密码或原密码
    """
    if not master_password:
        return encrypted_password
    return crypto_utils.decrypt_data(encrypted_password, master_password)


def get_master_password_from_settings(db) -> Optional[str]:
    """
    从系统设置中获取二级密码
    
    Args:
        db: 数据库会话
        
    Returns:
        Optional[str]: 二级密码，如果未设置则返回None
    """
    from .models import SystemSettings
    
    try:
        setting = db.query(SystemSettings).filter(
            SystemSettings.key == "master_password"
        ).first()
        
        if setting and setting.value:
            return setting.value
        return None
    except Exception as e:
        logger.error(f"获取二级密码失败: {str(e)}")
        return None
